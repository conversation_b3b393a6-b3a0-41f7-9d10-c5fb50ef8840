<template>
  <div class="page-container">
    <!-- 功能模块导航 -->
    <div class="app-card module-nav">
      <el-tabs v-model="activeModule" @tab-change="handleModuleChange">
        <el-tab-pane label="视频转换" name="video">
          <template #label>
            <span class="tab-label">
              <Icon icon="mdi:video-outline" class="tab-icon" />
              视频转换
            </span>
          </template>
        </el-tab-pane>
        <el-tab-pane label="音频提取" name="audio">
          <template #label>
            <span class="tab-label">
              <Icon icon="mdi:music-note-outline" class="tab-icon" />
              音频提取
            </span>
          </template>
        </el-tab-pane>
        <el-tab-pane label="语音识别" name="asr">
          <template #label>
            <span class="tab-label">
              <Icon icon="mdi:microphone" class="tab-icon" />
              语音识别
            </span>
          </template>
        </el-tab-pane>
        <el-tab-pane label="图片处理" name="image">
          <template #label>
            <span class="tab-label">
              <Icon icon="mdi:image-outline" class="tab-icon" />
              图片处理
            </span>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 音视频转换模块 -->
    <div v-show="activeModule === 'video'" class="module-content">
      <VideoConverter @file-uploaded="handleVideoFileUploaded" @conversion-started="handleConversionStarted"
        @conversion-completed="handleConversionCompleted" />
    </div>

    <!-- 音频提取模块 -->
    <div v-show="activeModule === 'audio'" class="module-content">
      <AudioExtractor @file-uploaded="handleAudioExtractFileUploaded" @extraction-started="handleExtractionStarted"
        @extraction-completed="handleExtractionCompleted" />
    </div>

    <!-- 语音识别模块 -->
    <div v-show="activeModule === 'asr'" class="module-content">
      <ASRProcessor @file-uploaded="handleAudioFileUploaded" @recognition-started="handleRecognitionStarted"
        @recognition-completed="handleRecognitionCompleted" />
    </div>

    <!-- 图片处理模块 -->
    <div v-show="activeModule === 'image'" class="module-content">
      <ImageProcessor @file-uploaded="handleImageFilesUploaded" @processing-started="handleImageProcessingStarted"
        @processing-completed="handleImageProcessingCompleted" />
    </div>


    <!-- 处理统计面板 -->
    <div class="app-card" v-if="showStats">
      <ProcessingStatsPanel :show-details="true" :show-clear-button="true" :auto-refresh="true"
        :refresh-interval="30000" @stats-updated="handleStatsUpdated" @stats-cleared="handleStatsCleared" />
    </div>

    <!-- 当前处理任务列表 -->
    <div class="app-card current-tasks" v-if="currentTasks.length > 0">
      <div class="section-header">
        <h3>当前任务</h3>
        <div class="header-actions">
          <el-button size="small" @click="pauseAllTasks">
            <Icon icon="mdi:pause" />
            全部暂停
          </el-button>
          <el-button size="small" type="danger" @click="cancelAllTasks">
            <Icon icon="mdi:trash-can-outline" />
            清空全部
          </el-button>
        </div>
      </div>

      <div class="task-list">
        <div v-for="task in currentTasks" :key="task.id" class="task-item"
          :class="{ 'task-error': task.status === 'error' }">
          <div class="task-info">
            <div class="task-icon">
              <Icon :icon="getTaskIcon(task.type)" :class="getTaskIconClass(task.status)" />
            </div>
            <div class="task-details">
              <div class="task-name">{{ task.fileName }}</div>
              <div class="task-meta">
                <span class="task-type">{{ getTaskTypeLabel(task.type) }}</span>
                <span class="task-status" :class="`status-${task.status}`">
                  {{ getTaskStatusLabel(task.status) }}
                </span>
              </div>
            </div>
          </div>

          <div class="task-progress">
            <el-progress :percentage="task.progress"
              :status="task.status === 'error' ? 'exception' : task.status === 'completed' ? 'success' : undefined"
              :show-text="false" />
            <div class="progress-text">{{ task.progress }}%</div>
          </div>

          <div class="task-actions">
            <el-button size="small" v-if="task.status === 'processing'" @click="pauseTask(task.id)">
              <Icon icon="mdi:pause" />
            </el-button>
            <el-button size="small" v-if="task.status === 'error'" @click="retryTask(task.id)">
              <Icon icon="mdi:refresh" />
            </el-button>
            <el-button size="small" type="danger" @click="removeTask(task.id)">
              <Icon icon="mdi:close" />
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 处理结果展示区域 -->
    <div class="app-card results-area" v-if="processingResults.length > 0">
      <div class="section-header">
        <h3>处理结果</h3>
        <el-button size="small" @click="clearResults">
          <Icon icon="mdi:trash-can-outline" />
          清空结果
        </el-button>
      </div>

      <div class="results-list">
        <div v-for="result in processingResults" :key="result.id" class="result-item">
          <div class="result-info">
            <div class="result-icon">
              <Icon :icon="getResultIcon(result.type)" class="success-icon" />
            </div>
            <div class="result-details">
              <div class="result-name">{{ result.fileName }}</div>
              <div class="result-meta">
                <span class="result-type">{{ getTaskTypeLabel(result.type) }}</span>
                <span class="result-time">{{ formatResultTime(result.completedAt) }}</span>
              </div>
            </div>
          </div>

          <div class="result-actions">
            <el-button size="small" @click="openResultFile(result.outputPath!)" v-if="result.outputPath">
              <Icon icon="mdi:folder-open-outline" />
              打开文件
            </el-button>
            <el-button size="small" @click="showResultDetails(result)">
              <Icon icon="mdi:information-outline" />
              详情
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Icon } from '@iconify/vue'
// 导入重构后的组件
import VideoConverter from '@/components/media/VideoConverter.vue'
import AudioExtractor from '@/components/media/AudioExtractor.vue'
import ASRProcessor from '@/components/media/ASRProcessor.vue'
import ImageProcessor from '@/components/media/ImageProcessor.vue'
import ProcessingStatsPanel from '@/components/media/shared/ProcessingStatsPanel.vue'
import { useMediaMainStore, useMediaTasksStore } from '@/stores'

// 使用重构后的 stores
const mainStore = useMediaMainStore()
const tasksStore = useMediaTasksStore()

// 直接使用store的计算属性，避免storeToRefs的问题
const allTasks = computed(() => tasksStore.allTasks)
const allResults = computed(() => Array.from(tasksStore.taskResults.values()))
const activeTasks = computed(() => tasksStore.activeTasks)
const isInitialized = computed(() => mainStore.isInitialized)

// 兼容性计算属性
const currentTasks = computed(() => activeTasks.value)
const processingResults = computed(() => allResults.value.slice(0, 10)) // 限制显示数量

// 响应式数据
const activeModule = ref('video')
const showStats = ref(true)

// 生命周期
onMounted(async () => {
  try {
    if (!isInitialized.value) {
      await mainStore.initialize()
    }
    await refreshStats()

    // 设置定期刷新统计信息
    setInterval(async () => {
      await refreshStats()
    }, 30000) // 每30秒刷新一次
  } catch (error) {
    console.error('初始化Media页面失败:', error)
    ElMessage.error('初始化失败，请刷新页面重试')
  }
})

// 事件处理方法
const handleModuleChange = (moduleName: string) => {
  console.log(`切换到模块: ${moduleName}`)
}

// 视频处理事件
const handleVideoFileUploaded = (fileInfo: any) => {
  console.log('视频文件上传:', fileInfo)
}

const handleConversionStarted = (taskInfo: any) => {
  console.log('视频转换开始:', taskInfo)
  ElMessage.success('视频转换已开始')
}

const handleConversionCompleted = (result: any) => {
  console.log('视频转换完成:', result)
  ElMessage.success('视频转换完成')
  refreshStats()
}

// 音频提取事件
const handleAudioExtractFileUploaded = (fileInfo: any) => {
  console.log('视频文件上传 (音频提取):', fileInfo)
}

const handleExtractionStarted = (taskInfo: any) => {
  console.log('音频提取开始:', taskInfo)
  ElMessage.success('音频提取已开始')
}

const handleExtractionCompleted = (result: any) => {
  console.log('音频提取完成:', result)
  ElMessage.success('音频提取完成')
  refreshStats()
}

// 语音识别事件
const handleAudioFileUploaded = (fileInfo: any) => {
  console.log('音频文件上传:', fileInfo)
}

const handleRecognitionStarted = (taskInfo: any) => {
  console.log('语音识别开始:', taskInfo)
  ElMessage.success('语音识别已开始')
}

const handleRecognitionCompleted = (result: any) => {
  console.log('语音识别完成:', result)
  ElMessage.success('语音识别完成')
  refreshStats()
}

// 图片处理事件
const handleImageFilesUploaded = (fileInfos: any[]) => {
  console.log('图片文件上传:', fileInfos)
}

const handleImageProcessingStarted = (taskInfo: any) => {
  console.log('图片处理开始:', taskInfo)
  ElMessage.success('图片处理已开始')
}

const handleImageProcessingCompleted = (result: any) => {
  console.log('图片处理完成:', result)
  ElMessage.success('图片处理完成')
  refreshStats()
}


// 任务控制方法
const pauseTask = async (taskId: string) => {
  try {
    const task = findTaskById(taskId)
    if (!task) {
      ElMessage.error('任务不存在')
      return
    }

    if (task.mode === 'single') {
      await mainStore.pauseSingleTask(taskId)
    } else {
      // 批量任务暂停功能待实现
      ElMessage.warning('批量任务暂停功能待实现')
    }

    ElMessage.success('任务已暂停')
  } catch (error: any) {
    ElMessage.error(`暂停任务失败: ${error.message}`)
  }
}

const retryTask = async (taskId: string) => {
  try {
    const task = findTaskById(taskId)
    if (!task) {
      ElMessage.error('任务不存在')
      return
    }

    // 重试功能待实现
    ElMessage.warning('重试功能待实现')

    ElMessage.success('任务重试中')
  } catch (error: any) {
    ElMessage.error(`重试任务失败: ${error.message}`)
  }
}

const removeTask = async (taskId: string) => {
  try {
    await ElMessageBox.confirm('确定要删除这个任务吗？', '确认删除', {
      type: 'warning'
    })

    const task = findTaskById(taskId)
    if (!task) {
      ElMessage.error('任务不存在')
      return
    }

    await tasksStore.removeSingleTask(taskId)

    ElMessage.success('任务已删除')
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(`删除任务失败: ${error.message}`)
    }
  }
}

const pauseAllTasks = async () => {
  try {
    // 暂停所有任务
    for (const task of activeTasks.value) {
      if (task.status === 'processing') {
        await mainStore.pauseSingleTask(task.id)
      }
    }
    ElMessage.success('所有任务已暂停')
  } catch (error: any) {
    ElMessage.error(`暂停任务失败: ${error.message}`)
  }
}

const cancelAllTasks = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有任务吗？这将删除所有进行中和等待中的任务。',
      '确认清空',
      {
        type: 'warning'
      }
    )
    await tasksStore.clearAllTasks()
    ElMessage.success('所有任务已清空')
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(`清空任务失败: ${error.message}`)
    }
  }
}

// 统计相关方法
const refreshStats = async () => {
  try {
    // 刷新统计功能待实现
  } catch (error) {
    console.error('刷新统计失败:', error)
  }
}

// 结果相关方法
const clearResults = async () => {
  try {
    await ElMessageBox.confirm('确定要清空所有结果吗？', '确认清空', {
      type: 'warning'
    });
    // 清空结果
    tasksStore.taskResults.clear();
    ElMessage.success('结果已清空');
  } catch (error: unknown) {
    if (error !== 'cancel') {
      ElMessage.error(`清空结果失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }
}

const openResultFile = async (filePath: string) => {
  try {
    await window.electronAPI.app.showItemInFolder(filePath)
  } catch (error: any) {
    ElMessage.error(`打开文件失败: ${error.message}`)
  }
}

const showResultDetails = (result: any) => {
  // TODO: 实现结果详情展示
  console.log('显示结果详情:', result)
}

// 统计面板事件处理
const handleStatsUpdated = (stats: any) => {
  console.log('统计数据已更新:', stats)
}

const handleStatsCleared = () => {
  console.log('统计数据已清空')
  ElMessage.success('统计数据已清空')
}

// 工具方法
const getTaskIcon = (type: string): string => {
  const icons = {
    'video-convert': 'mdi:video-outline',
    'audio-extract': 'mdi:music-note',
    'asr': 'mdi:microphone',
    'image-process': 'mdi:image-outline'
  }
  return icons[type] || 'mdi:file-outline'
}

const getTaskIconClass = (status: string): string => {
  const classes = {
    'pending': 'pending-icon',
    'processing': 'processing-icon',
    'completed': 'success-icon',
    'error': 'error-icon'
  }
  return classes[status] || ''
}

const getTaskTypeLabel = (type: string): string => {
  const labels = {
    'video-convert': '视频转换',
    'audio-extract': '音频提取',
    'asr': '语音识别',
    'image-process': '图片处理'
  }
  return labels[type] || type
}

const getTaskStatusLabel = (status: string): string => {
  const labels = {
    'pending': '等待中',
    'processing': '处理中',
    'completed': '已完成',
    'error': '失败'
  }
  return labels[status] || status
}

const getResultIcon = (type: string): string => {
  return getTaskIcon(type)
}


// 工具方法：根据ID查找任务
const findTaskById = (taskId: string) => {
  return allTasks.value.find(task => task.id === taskId)
}

const formatResultTime = (timestamp: number): string => {
  return new Date(timestamp).toLocaleString()
}
</script>

<style scoped lang="scss">
@use '@/assets/styles/index.scss' as *;

.module-nav {
  .tab-label {
    display: flex;
    align-items: center;
    gap: 6px;

    .tab-icon {
      font-size: 16px;
    }
  }
}

.module-content {
  margin-top: $spacing-base;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-base;

  h3 {
    margin: 0;
    color: $text-primary;
    font-size: 16px;
    font-weight: 500;
  }

  .header-actions {
    display: flex;
    gap: $spacing-small;
  }
}

.stats-overview {
  margin-top: $spacing-base;
}

.current-tasks {
  margin-top: $spacing-base;
}

.task-list {
  .task-item {
    display: flex;
    align-items: center;
    padding: $spacing-base;
    border: 1px solid $border-lighter;
    border-radius: $border-radius-base;
    margin-bottom: $spacing-small;
    background: $background-light;
    transition: $transition-base;

    &:hover {
      background: $background-card;
      border-color: $border-light;
    }

    &.task-error {
      border-color: $danger-color;
      background: #fef0f0;
    }

    .task-info {
      display: flex;
      align-items: center;
      flex: 1;

      .task-icon {
        margin-right: $spacing-base;
        font-size: 20px;

        .pending-icon {
          color: $info-color;
        }

        .processing-icon {
          color: $primary-color;
        }

        .success-icon {
          color: $success-color;
        }

        .error-icon {
          color: $danger-color;
        }
      }

      .task-details {
        .task-name {
          font-weight: 500;
          color: $text-primary;
          margin-bottom: 4px;
        }

        .task-meta {
          display: flex;
          gap: $spacing-small;
          font-size: 12px;

          .task-type {
            color: $text-secondary;
          }

          .task-status {
            padding: 2px 6px;
            border-radius: 4px;

            &.status-pending {
              background: #f0f0f0;
              color: $info-color;
            }

            &.status-processing {
              background: #e6f7ff;
              color: $primary-color;
            }

            &.status-completed {
              background: #f6ffed;
              color: $success-color;
            }

            &.status-error {
              background: #fff2f0;
              color: $danger-color;
            }
          }
        }
      }
    }

    .task-progress {
      flex: 2;
      margin: 0 $spacing-base;

      .progress-text {
        text-align: center;
        font-size: 12px;
        color: $text-secondary;
        margin-top: 4px;
      }
    }

    .task-actions {
      display: flex;
      gap: $spacing-small;
    }
  }
}

.results-area {
  margin-top: $spacing-base;
}

.results-list {
  .result-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: $spacing-base;
    border: 1px solid $border-lighter;
    border-radius: $border-radius-base;
    margin-bottom: $spacing-small;
    background: $background-light;

    &:hover {
      background: $background-card;
      border-color: $border-light;
    }

    .result-info {
      display: flex;
      align-items: center;

      .result-icon {
        margin-right: $spacing-base;
        font-size: 20px;
        color: $success-color;
      }

      .result-details {
        .result-name {
          font-weight: 500;
          color: $text-primary;
          margin-bottom: 4px;
        }

        .result-meta {
          display: flex;
          gap: $spacing-small;
          font-size: 12px;
          color: $text-secondary;
        }
      }
    }

    .result-actions {
      display: flex;
      gap: $spacing-small;
    }
  }
}

// 动画效果
.task-item,
.result-item {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>